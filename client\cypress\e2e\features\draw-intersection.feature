Feature: Draw Intersection

  Background:
    Given The user searches for and opens file "E2E-Draw-Intersection.mp4"

  # @e2e @draw-intersection
  # Scenario: Verify UI
  #   And The following information should be visible on the page:
  #     | Field            | Value                     |
  #     | File Heading     | E2E-Draw-Intersection.mp4 |
  #     | Event Breadcrumb | April 050                 |

  # @e2e @draw-intersection
  # Scenario: Verify user can draw a region box on media player
  #   When The user draws "1st" region box at coordinates 100, 100, 200, 200
  #   Then The region box should be visible on the media player

  # @e2e @draw-intersection
  # Scenario: Verify user can draw only one region box on media player
  #   When The user draws "1st" region box at coordinates 100, 100, 200, 200
  #   Then The region box should be visible on the media player
  #   When The user draws "2nd" region box at coordinates 150, 150, 250, 250
  #   Then Only one region box should be visible on the media player

  # @e2e @draw-intersection
  # Scenario: Verify user can move region box freely
  #   When The user draws "1st" region box at coordinates 100, 100, 200, 200
  #   Then The region box should be visible on the media player
  #   When The user moves the region box from coordinates 150, 150 to 250, 250
  #   Then The region box should be visible on the media player

  @e2e @draw-intersection
  Scenario: Verify user can remove a region by clicking Reset Selection button
    When The user draws "1st" region box at coordinates 100, 100, 200, 200
    Then The region box should be visible on the media player
    When The user clicks on "Reset Selection" button
    Then The region box should not be visible on the media player

  @e2e @draw-intersection
  Scenario: Verify user can remove a region by clicking Remove in region menu
    When The user draws "1st" region box at coordinates 100, 100, 200, 200
    Then The region box should be visible on the media player
    When The user clicks on the region box
    And The user clicks on "Remove" in the context menu
    Then The region box should not be visible on the media player