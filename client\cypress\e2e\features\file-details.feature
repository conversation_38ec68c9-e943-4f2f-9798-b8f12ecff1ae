Feature: File Details

  @e2e @file-details
  Scenario: Verify user can go to File Details by 2 ways
    Given The user is on Event Screen
    When The user enters "lucy" into the search bar
    Then The displayed event results should contain "lucy"
    When The user selects the "event" named "lucy"
    And The user clicks on "View Event"
    Then The user should navigate to event details page
    When The user clicks the row with file name "swahili-mp4.mp4" and status "PROCESSED"
    And The user clicks the "View File" button on the details panel
    Then The File Viewer page should be displayed successfully
    Given The user is on File Screen
    When The user enters "swahili-mp4.mp4" into the search bar
    Then The displayed file results should contain "swahili-mp4.mp4"
    When The user selects the "file" named "swahili-mp4.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully

  @e2e @file-details
  Scenario: Verify user can edit file name
    Given The user is on File Screen
    When The user enters "swahili-mp4.mp4" into the search bar
    Then The displayed file results should contain "swahili-mp4.mp4"
    When The user selects the "file" named "swahili-mp4.mp4"
    And The user clicks the "file" name located on the right side
    And The user changes "file" name to "swahilily-mp4.mp4"
    Then The user see the notification message "Updated file name successfully"
    Then The name of the "file" should be updated to "swahilily-mp4.mp4"
    And The user clicks the "file" name located on the right side
    And The user changes "file" name to "swahili-mp4.mp4"
    Then The user see the notification message "Updated file name successfully"
    Then The name of the "file" should be updated to "swahili-mp4.mp4"

  @e2e @file-details
  Scenario: Verify UI of file when no detection thumbnail is selected
    Given The user is on File Screen
    When The user enters "swahili-mp4.mp4" into the search bar
    Then The displayed file results should contain "swahili-mp4.mp4"
    When The user selects the "file" named "swahili-mp4.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    And The following information should be visible on the page:
      | Field            | Value                   |
      | File Heading     | swahili-mp4.mp4         |
      | Event Breadcrumb | lucy                    |
      | Detections Panel | No detections Detected. |

  @e2e @file-details
  Scenario: Verify UI updates after selecting a detection thumbnail
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user clicks on the "1st" detection thumbnail
    Then The following UI elements should be updated in the details panel:
      | Element             | State / Value |
      | Find Matches Button | enabled       |
      | Detected Attributes | visible       |
      | File Metadata       | visible       |
      | Detected Attribute  | Any Bag       |
      | Detected Attribute  | Female        |

  @e2e @file-details
  Scenario: Verify video jumps to the scene when a detection is selected
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user clicks on the "6th" detection thumbnail
    Then the video should jump to the detection time and be paused

  @e2e @file-details
  Scenario: Verify user can navigate back using breadcrumbs
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user clicks the "All Events" breadcrumb
    Then The user should see the correct column headers for Event Name and Event Time

  @e2e @file-details
  Scenario: Verify user can switch between accordion summaries and close both
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user clicks on the "6th" detection thumbnail
    Then The "Detected Attributes" accordion should be "expanded"
    When The user clicks the "File Metadata" accordion
    Then The "File Metadata" accordion should be "expanded"
    And The "Detected Attributes" accordion should be "collapsed"
    When The user clicks the "File Metadata" accordion
    And The "File Metadata" accordion should be "collapsed"
    Then The "Detected Attributes" accordion should be "collapsed"

  @e2e @file-details
  Scenario: Verify user can find matches with a new match group
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user clicks on the "6th" detection thumbnail
    When The user clicks the "Find Matches" button
    And The user creates a new match group named "E2E-Find-Matches"
    Then The user see the notification message "Match Group E2E-Find-Matches was created successfully"
    And The user clicks the "Continue" button in the popover
    Then The user see the notification message "Match Group E2E-Find-Matches was updated successfully"
    When The user clicks on the "7th" detection thumbnail
    When The user clicks the "Find Matches" button
    And The user selects the existing match group "E2E-Find-Matches"
    And The user clicks the "Continue" button in the popover
    Then The user see the notification message "Match Group E2E-Find-Matches was updated successfully"
    When The user clicks the "Tuck07-21-25" breadcrumb
    And The user clicks the "Match Groups" tab
    When The user opens the menu for the "E2E-Find-Matches" group
    And The user selects the "Delete" option from the menu
    And The user confirms the deletion
    Then The user see the notification message "MatchGroup successfully deleted"

  @e2e @file-details
  Scenario: Verify user can use widgets in video preview
    Given The user is on File Screen
    When The user enters "swahili-mp4" into the search bar
    Then The displayed file results should contain "swahili-mp4.mp4"
    When The user selects the "file" named "swahili-mp4.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user clicks the "Play" video control
    Then The video should be "playing"
    When The user clicks the "Pause" video control
    Then The video should be "paused"
    When The user sets the video playback speed to "1.5x"
    Then The video playback speed should be 1.5

  @e2e @file-details
  Scenario: Verify user can open thumbnail scale slider
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user opens the thumbnail scale slider
    Then The thumbnail scale should display "100%"

  @e2e @file-details
  Scenario: Verify user can filter by object attributes
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user filters detections by attribute "Face" and value "Back"
    Then The number of visible thumbnails should be 6

  @e2e @file-details
  Scenario: Verify File Metadata
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user clicks on the "1st" detection thumbnail
    And The user clicks the "File Metadata" accordion
    Then The following file metadata should be displayed:
      | Field             | Value         |
      | File Name         | StreetCam.mp4 |
      | Upload Date       | 7/22/2025     |
      | File GPS Location | unavailable   |
      | File Type         | video/mp4     |
      | File Size         | 10.9 Mb       |
      | Video Length      | 00:00:15      |

  @e2e @file-details
  Scenario: Verify user can change the number of rows per page
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    Then The results per page should be "100"
    When The user changes the results per page to the following values:
      | Count |
      |    50 |
      |    10 |
      |   100 |

  @e2e @file-details
  Scenario: Verify user can move between pages
    Given The user is on File Screen
    When The user enters "StreetCam" into the search bar
    Then The displayed file results should contain "StreetCam.mp4"
    When The user selects the "file" named "StreetCam.mp4"
    And The user clicks on "View File"
    Then The File Viewer page should be displayed successfully
    When The user changes the results per page to the following values:
      | Count |
      |    10 |
    Then the current page number should be 1
    When The user clicks the "Next" page button
    Then the current page number should be 2
    When The user clicks the "Previous" page button
    Then the current page number should be 1
