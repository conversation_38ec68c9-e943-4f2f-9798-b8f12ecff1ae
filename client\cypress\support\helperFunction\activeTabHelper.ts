export enum ButtonTestIds {
  VIEW_EVENT = 'home-view-event-button',
  DELETE_EVENT = 'home-delete-event-button',
  DELETE_FILE = 'home-delete-file-button',
  VIEW_FILE = 'home-view-file-button',
  EVENT_NAME_EDIT = 'home-detail-event-name-edit',
  FILE_NAME_EDIT = 'home-detail-file-name-edit',
}

export enum DialogTestIds {
  DELETE_INPUT = 'confirm-dialog-delete-input',
  CONFIRM_ACTION = 'confirm-dialog-confirm-action',
  CANCEL_ACTION = 'confirm-dialog-cancel-action',
}

export enum ButtonActions {
  VIEW_EVENT = 'View Event',
  DELETE_EVENT = 'Delete Event',
  VIEW_FILE = 'View File',
  DELETE_FILE = 'Delete File'
}

export enum ValidationButtonTypes {
  DELETE = 'Delete',
}

export interface ButtonSelector {
  selector: ButtonTestIds;
}

export interface DialogSelector {
  input: DialogTestIds;
  confirmButton: DialogTestIds;
}

export interface ButtonActionMap {
  [key: string]: () => void;
}

export interface EventDetailRow {
  Field: string;
  'Expected Value': string;
}

export enum SearchTestIds {
  SEARCH_INPUT = 'search-and-upload-search_input',
  SEARCH_POPUP = 'search-and-upload-upload-popover-select-event-input'
}

export enum UploadTestIds {
  PROGRESS_TEXT = 'search-and-upload__files-file-progress-text',
}

export enum SnackbarTestIds {
  SNACKBAR_BOX = 'snackbar-box-1-1',
  SNACKBAR_BOX_SUCCESS = 'snackbar-box-success',
}

export const buttonSelectors: Record<ButtonTestIds, ButtonSelector> = {
  [ButtonTestIds.VIEW_EVENT]: { selector: ButtonTestIds.VIEW_EVENT },
  [ButtonTestIds.DELETE_EVENT]: { selector: ButtonTestIds.DELETE_EVENT },
  [ButtonTestIds.EVENT_NAME_EDIT]: { selector: ButtonTestIds.EVENT_NAME_EDIT },
  [ButtonTestIds.VIEW_FILE]: { selector: ButtonTestIds.VIEW_FILE },
  [ButtonTestIds.FILE_NAME_EDIT]: { selector: ButtonTestIds.FILE_NAME_EDIT },
  [ButtonTestIds.DELETE_FILE]: { selector: ButtonTestIds.DELETE_FILE }
};

export const dialogSelectors: DialogSelector = {
  input: DialogTestIds.DELETE_INPUT,
  confirmButton: DialogTestIds.CONFIRM_ACTION,
};

export const enum LandingPageTestIds {
  COLUMN_0 = 'column-0',
  COLUMN_1 = 'column-1',
  COLUMN_2 = 'column-2',
  APPBAR_TITLE = 'appbarTitle',
  HOME_DETAIL_NAME = 'home-detail-name',
  HOME_DETAIL_EVENT_NAME_TEXTFIELD = 'home-detail-event-name-textfield',
  HOME_DETAIL_FILE_NAME_TEXTFIELD = 'home-detail-file-name-textfield',
  HOME_DETAIL_CREATED_DATE = 'home-detail-created-date',
  HOME_DETAIL_CREATOR_NAME = 'home-detail-creator-name',
  HOME_DETAIL_FILE_COUNT = 'home-detail-file-count',
  HOME_DETAIL_MATCHGROUP_COUNT = 'home-detail-matchgroup-count',
  HOME_VIEW_EVENT_BUTTON = 'home-view-event-button',
  HOME_DELETE_EVENT_BUTTON = 'home-delete-event-button',
  EVENT_CONTAINER = 'event-container',
  EVENT_ROW_NAME = 'event-row-name',
  FILE_ROW_NAME = 'file-row-name',
  RESULTS_PER_PAGE_DROPDOWN = 'table-pagination-page-size',
  PAGINATION_TEXT = 'table-pagination-page-selector-text',
  PAGINATION_NEXT_BUTTON = 'table-pagination-page-selector-next',
  PAGINATION_PREVIOUS_BUTTON = 'table-pagination-page-selector-back',
  UPLOAD_FILE_BUTTON = 'search-and-upload-upload-button',
  NEW_EVENT_BUTTON = 'search-and-upload-detail-upload-browse-add',
  NEW_EVENT_INPUT = 'search-and-upload-detail-upload-new-event-input',
  NEW_EVENT_CONFIRM_BUTTON = 'search-and-upload-detail-upload-browse-confirm',
  UPLOAD_BUTTON = 'search-and-upload-detail-upload-controls-upload',
  UPLOAD_FILE_INPUT = 'drop-input',
  UPLOAD_FILES_FILE = 'search-and-upload-files-file',
  UPLOAD_FILES_FILE_IDLE = 'search-and-upload-files-file-idle',
  UPLOAD_FILES_FILE_COMPLETE = 'search-and-upload-files-file-complete',
  PENDING_FILE_ROW = 'pending-file-row',
  HOME_FILES_TAB = 'home-files-tab',
  HOME_EVENTS_TAB = 'home-events-tab',
  HOME_VIEW_FILE_BUTTON = 'home-view-file-button',
}

export const getIndexFromOrdinal = (ordinal: string): number => {
  const numberPart = parseInt(ordinal, 10);
  if (isNaN(numberPart)) {
    throw new Error(`Could not parse number from ordinal string: ${ordinal}`);
  }
  return numberPart - 1;
};

export const eventDetailSelectors: {
  [key: string]: {
    selector: string;
    assertionType: 'have.text' | 'contain.text';
  };
} = {
  'Event Name': { selector: LandingPageTestIds.HOME_DETAIL_NAME, assertionType: 'have.text' },
  Date: { selector: LandingPageTestIds.HOME_DETAIL_CREATED_DATE, assertionType: 'have.text' },
  'Event Creator': { selector: LandingPageTestIds.HOME_DETAIL_CREATOR_NAME, assertionType: 'have.text' },
  'File Count': { selector: LandingPageTestIds.HOME_DETAIL_FILE_COUNT, assertionType: 'have.text' },
  'Match Group Count': { selector: LandingPageTestIds.HOME_DETAIL_MATCHGROUP_COUNT, assertionType: 'have.text' },
  'View Event Button': { selector: LandingPageTestIds.HOME_VIEW_EVENT_BUTTON, assertionType: 'contain.text' },
  'Delete Event Button': { selector: LandingPageTestIds.HOME_DELETE_EVENT_BUTTON, assertionType: 'contain.text' },
};

export const fileDetailSelectors: {
  [key: string]: {
    selector: string;
    assertionType: 'have.text' | 'contain.text';
  };
} = {
  'File Name': { selector: 'home-detail-name', assertionType: 'have.text' },
  'Upload Date': { selector: 'home-detail-date', assertionType: 'have.text' },
  'File Creator': { selector: 'home-detail-creator', assertionType: 'have.text' },
  'File Size': { selector: 'home-detail-file-size', assertionType: 'have.text' },
  'GPS Location': { selector: 'home-detail-file', assertionType: 'have.text' },
  'View File Button': { selector: 'home-view-file-button', assertionType: 'contain.text' },
  'Delete File Button': { selector: 'home-delete-file-button', assertionType: 'contain.text' },
};
