import './MatchGroupDialog.scss';
import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, IconButton, styled, TextField } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const DialogButton = styled(Button)(() => ({
  textTransform: 'none',
}));

const ConfirmDialog = ({
  open,
  title,
  content,
  confirmText,
  onConfirm,
  onClose,
  value,
  isDelete
}: ConfirmDialogProps) => {
  const [input, setInput] = useState("");

  const isDisabled = !!value && (input.trim() === value || input.trim() === '');

  const handleChangeInput = (e: ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleClose = () => {
    onClose();
    setInput(value ? value : '');
  };

  const handleConfirm = () => {
    if (value && input.trim() !== '') {
        onConfirm(input.trim());
    } else if (isDelete) {
        onConfirm();
    }
  };

  useEffect(() => setInput(() => value ? value : ""), [value]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="confirm-dialog-title"
      aria-describedby="confirm-dialog-description"
      PaperProps={{
        style: {
          width: 500,
          padding: '12px 0',
          borderRadius: 10
        },
      }}
      data-testid="confirm-dialog-matchGroup"
    >
      <DialogTitle
        className="confirm-dialog-title"
      >
        <span>{title}</span>
        <IconButton onClick={handleClose}>
            <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        className='confirm-dialog-content'
        sx={{ margin: isDelete ? '0 65px 15px 0': '' }}
      >
        <DialogContentText className="confirm-dialog-description">
          {content}
        </DialogContentText>
        {value && (
          <TextField
            value={input}
            fullWidth 
            size='small'
            onChange={handleChangeInput}
            error={input === ''}
            placeholder='Enter new name'
            slotProps={{ 
              htmlInput: { 
                'data-testid': 'confirm-dialog-rename-input'
              }
            }}
          />
        )}
      </DialogContent>
      <DialogActions sx={{ pr: '25px', gap: '10px' }}>
        <DialogButton variant='outlined' onClick={handleClose}>
          Cancel
        </DialogButton>
        <DialogButton
          onClick={handleConfirm}
          variant="contained"
          color={isDelete ? 'error' : 'primary'}
          autoFocus
          disabled={isDisabled}
          sx={{ borderRadius: 5 }}
          data-testid="confirm-dialog-confirm-action"
        >
          {confirmText}
        </DialogButton>
      </DialogActions>
    </Dialog>
  );
};


interface ConfirmDialogProps {
  open: boolean;
  title: string;
  content?: string;
  confirmText: string;
  onConfirm: (arg?: string) => void;
  onClose: () => void;
  value?: string;
  isDelete?: boolean;
}

export default ConfirmDialog;
